<template>
    <q-card-section class="q-py-none" v-if="hasPermissionForTeam(team, 'team.subscription.manage')">
        Abo Status:
        <span v-if="!team.hasTrialEndDate()">
          Kostenlose Version
        </span>
        <template v-else>
          <span v-if="!team.isOnTrial && !team.isSubscribed">
            Testzeitraum abgelaufen am {{team.getTrialEndsAtFormatted()}}
          </span>
          <span v-if="team.isOnTrial">
            kostenloser Test bis {{team.getTrialEndsAtFormatted()}}
            <span v-if="team.isSubscribed">, danach</span>
          </span>
          <span v-if="team.isSubscribed">
            <span v-if="team.hasSubscriptionEndDate()">
              Beendet, Restlaufzeit bis {{team.getSubscriptionEndsAtFormatted()}}
            </span>
            <span v-else>
              Aktiv
            </span>
          </span>
        </template>
    </q-card-section>
</template>

<script setup lang="ts">
    import Team from 'src/models/Team';

    const props = defineProps<{
        team: Team
    }>();
</script>

<style lang="scss" scoped></style>
