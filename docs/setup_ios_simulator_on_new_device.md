In order to run the iOS build and simulator on a new Macbook the following steps are required:
1. Install Xcode from the App Store
2. Open Xcode and accept the license agreement
3. Select iOS as a target platform and wait for <PERSON><PERSON> to download the necessary support files
4. Run the following command in the terminal: `sudo xcode-select -s /Applications/Xcode.app/Contents/Developer`
5. Install an up to date version of ruby via `brew install ruby`
6Add this to your .zshrc:
   `export PATH="/opt/homebrew/opt/ruby/bin:$PATH"`
   `export PATH="$PATH:$(ruby -r rubygems -e 'puts Gem.user_dir')/bin"`
6. Update Cocoapods via `sudo gem install cocoapods`
7. Install pods via `pod install`
