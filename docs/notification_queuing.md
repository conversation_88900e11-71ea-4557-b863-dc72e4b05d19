
## Description

Notifications are send out via a queue maintained within the databases `jobs` table.

## Usage

1. The `Notification` needs to implement the `ShouldQueue` interface and inherit the `Queueable` trait.
2. Call the `->notify()` method on a `notifiable` as usual. <PERSON><PERSON> will notice the `ShouldQueue` interface and schedule a job for every notifiable and channel.

## How this works

The current Ionos VPS has cron jobs for the staging and production backend configured to trigger the `schedule:run` command every minute. 

Crontab line (editable via `crontab -e`):
```cronexp
* * * * * cd /kunden/homepages/18/d950750563/htdocs/staging/backend && php8.3-cli artisan schedule:run >> /dev/null 2>&1
* * * * * cd /kunden/homepages/18/d950750563/htdocs/production/backend && php8.3-cli artisan schedule:run >> /dev/null 2>&1
```

The `schedule:run` will run the scheduled work configured in the `App\Console\Kernel` class. Right now this means running `queue:work --stop-when-empty`. 
This command will run the next job in the queue and stop when the queue is empty.