## Enabling a feature for a certain person
1. Look up the person in the database by name and note their ID
2. Go into the features table and find the person and feature you want to enable
3. Set the feature to true

## Adding new super admins
1. Go to [server/app/Providers/FeatureServiceProvider.php](../server/app/Providers/FeatureServiceProvider.php)
2. Modify the `isSuperAdmin` function as required

## Notes
- The admin.stats feature isn't just used for the stats. It also controls global visibility of all teams and the staging/prod switch.