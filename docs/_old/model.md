```ts
class TeamEntity {
    members: [TeamMemberEntity]
}

class TeamMemberEntity {
    name: string
    person: PersonEntity
}

class PersonEntity {
    name: string
    admin: LoginEntity
}

class LoginEntity {
    cognitoId: string
    person: [PersonRelation]
}

class PersonRelation {
    person: PersonEntity
    relationType: string // vater, mutter, ich
}
```

```json
[
    {cognitoId: 'c_id1', person: [
        {person: 'id_p1', relationType: 'ich'},
        {person: 'id_p2', relationType: 'vater'}
    ]},
]
```
