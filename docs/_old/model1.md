```ts
class TeamEntity {
    members: [TeamMemberEntity]
}

class TeamMemberEntity {
    name: string
    person: PersonEntity
}

class PersonEntity {
    name: string
    admin: LoginEntity
}

class LoginEntity {
    cognitoId: string
    person: PersonEntity

    relationType: string
}

class InviteLinkEntity {
    id: uuid
    teamMember: x
    team: x
}
```

### Neues Team anlegen
1. Falls eingeloggt [Login bekannt]
   - sonst überspringen, registrieren am Ende? 
2. Teamname abfragen [Team anlegen]
3. TeamMember eintragen [TeamMember anlegen]
   - Erster Eintrag ist man selbst (in der GUI anzeigen)
        - Login hat Person "me" => [Person bekannt] Name nur anzeigen
        - Login hat keine Person "me" => [Person anlegen] Name eintragen
   - Minimum Anzahl an weiteren Membern?


### Team Member einem Login zuordnen
Hallo bob,
hier der einladungslink: http://nicht.sicher/<uuid>

**(Team Member hat noch keinen Login, also noch keine Person zugeordnet)**

1. <PERSON><PERSON> rausfinden (einloggen, registrieren)
   - <PERSON><PERSON> von <PERSON> (und zugehörige Personen + relation)
2. TeamMember rausfinden (Info über team member wird angezeigt, oder aus Team auswählen)
3. Person + Relation rausfinden: vater, mutter, me
   - me:
        - Login hat Person "me" => [Person bekannt]
        - Login hat keine Person "me" => [Person anlegen]
   - Vater / Sonstige:         
        - Login hat Person mit Relation != me 
            - => alle Personen anzeigen und fragen ob es eine davon ist
                - ja => [Person bekannt]
                - nein => [Person anlegen]
        - Login hat keine Person mit gleicher Relation
            - => [Person anlegen]

5. login x, person y -> team member z
  

[PersonEntity] 
    anlegen (mit gleichem Namen des TeamMembers)
    oder bekannte verwenden
[LoginEntity] anlegen und Person zuordnen
[TeamMember] Person zuordnen








```json
[
    {cognitoId: 'c_id1', person: 'id_p1', relationType: 'awesome_me'},
    {cognitoId: 'c_id1', person: 'id_p2', relationType: 'vater'}
]
```
