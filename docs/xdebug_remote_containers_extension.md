When running VSCode using the remote containers extension directly within the web container of ddev, you need to update the xdebug.client_host to 127.0.0.1. This can be done by creating an .ddev/php/xdebug.ini file with `xdebug.client_host=127.0.0.1` as the content. A restart of the containers shouldn't be necessary and the configuration should automatically be added to the php.ini file by ddev. Don't forget to remove this again when running VSCode or PHPStorm from the host machine.