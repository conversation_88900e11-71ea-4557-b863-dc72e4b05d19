This document describes how to run a development server with SSL enabled.

By running the development server using SSL and redirecting traffic from app-staging.numo-app.com to your local development machine, you can test your production mobile app with the development server. This is especially useful for testing push notification related code.

## Generate certificates
1. Install certbot via `brew install certbot`
2. Run `sudo certbot -d app-testing.numo-app.com,api-testing.numo-app.com --manual --preferred-challenges dns certonly`
   1. Create the requested challenges in the Ionos DNS settings.
3. Fix permissions: `sudo chown -R $USER /etc/letsencrypt`

## Ensure domains are redirected to your local machine
1. Use PiHole or AdGuard Home to redirect app-testing.numo-app.com and api-testing.numo-app.com to your local machine.

## Run the development servers
1. Run the frontend dev server via `yarn dev:ssl` (uses a different .env file with the staging URLs configured)
2. Run the backend dev server via `ddev start`

## Run the SSL proxy
1. In `clubmanager/tooling` run `./run_ssl_proxy`
