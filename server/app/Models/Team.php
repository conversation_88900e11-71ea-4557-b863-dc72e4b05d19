<?php

namespace App\Models;

use App\Events\TeamCreated;
use App\Http\Controllers\AuthController;
use App\Traits\HasInvite;
use App\Types\TeamRoleType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Relations\HasOne;


class Team extends Model {
    use HasUuids, HasInvite;

    protected $fillable = [
        'name',
        'icon_name',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'trial_ends_at' => 'datetime',
    ];

    /**
     * @var array<string, class-string> 
     */
    protected $dispatchesEvents = [
        'created' => TeamCreated::class,
    ];
    
    protected static function booted() {
        static::creating(function (Team $team) {
            $team->trial_ends_at = self::getTrialEndsAtForNewTeam();
        });

        static::created(function (Team $team) {
            TeamRoleType::setDefaultRolesAndPermissions($team);
            $user = AuthController::getCurrentUser();
            if (isset($user)) {
                $manager = $team->createDefaultManagerMemberFromUser($user);
                $manager->roles()->attach($team->getTeamRole(TeamRoleType::TREASURER));
            }
        });
    }

    /**
     * Can be used to make an extra long trial available
     * @return Carbon|null
     */
    public static function getMinimumTrialEndDate(): ?Carbon {
        return Carbon::create(2025, 01, 15); // https://github.com/eltini/clubmanager/issues/695
    }
    
    public static function getTrialEndsAtForNewTeam(): Carbon|null {
        $minimumTrialDate = self::getMinimumTrialEndDate(); 
        /** @var int $trialDaysTeam */
        $trialDaysTeam = config('cashier.trial_days.team');
        $trialEndsAt = now()->endOfDay()->addDays($trialDaysTeam);
        if ($trialEndsAt < $minimumTrialDate) {
            $trialEndsAt = $minimumTrialDate;
        }
        return $trialEndsAt;
    }

    private function createDefaultManagerMemberFromUser(User $user): TeamMember {
        return $this->createMember($user->person->getFullName(), TeamRoleType::MANAGER, $user);
    }

    public function createMember(
        string $name = 'new', 
        TeamRoleType $teamRoleType = TeamRoleType::MEMBER,
        User $user = null,
        Carbon $joinDate = null
    ): TeamMember {
        $role = $this->getTeamRole($teamRoleType);
        $member = $this->members()->create([
            'name'         => $name,
            'team_role_id' => $role->id,
            'join_date' => $joinDate
        ]);
        $member->statusRole()->associate($role)->save();
        if ($user instanceof User) {
            $user->person->teamMembers()->saveQuietly($member);
        }
        return $member;
    }

    public function getTeamRole(TeamRoleType $type): TeamRole {
        return $this->roles->where('name', $type)->first();
    }

    /**
     * @return HasMany<TeamMember, $this>
     */
    public function members(): HasMany {
        return $this->hasMany(TeamMember::class, 'team_id');
    }


    /**
     * @return HasMany<Subscription, $this>
     */
    public function subscriptions(): HasMany {
        return $this->hasMany(Subscription::class, 'team_id');
    }

    /**
     * @return Hasone<Subscription, $this>
     */
    public function activeSubscription(): HasOne {
        return $this->hasOne(Subscription::class, 'team_id')
            ->active()
            ->orderByDesc('created_at');
            
    }

    /**
     * Get the active subscription instance
     *
     * @return Subscription|null
     */
    public function getActiveSubscription(): ?Subscription {
        return $this->activeSubscription()->first();
    }

    public function isSubscribed(): bool {
        return $this->getActiveSubscription() instanceof Subscription;
    }

    public function getSubscriptionEndsAt(): ?\Carbon\Carbon {
        return $this->getActiveSubscription()?->onGracePeriod() ? $this->getActiveSubscription()->ends_at : null;
    }
    
    public function isOnTrial(): bool {
        return $this->trial_ends_at === null || $this->trial_ends_at->isFuture();
    }

    /**
     * @return HasMany<TeamMember, $this>
     */
    public function activeMembers(): HasMany {
        return $this->members()->active();
    }

    /**
     * @return HasMany<TeamMember, $this>
     */
    public function registeredMembers(): HasMany {
        return $this->members()->registered();
    }

    /**
     * @return HasMany<TeamEvent, $this>
     */
    public function events(): HasMany {
        return $this->hasMany(TeamEvent::class, 'team_id');
    }

    /**
     * @return HasManyThrough<TeamEventVote, $this>
     */
    public function votes(): HasManyThrough {
        return $this->hasManyThrough(
            TeamEventVote::class,
            TeamEvent::class,
            'team_id',       // Foreign key on Team (team_id)
            'team_event_id',  // Foreign key on TeamEventVote
            'id',  // Local key on Team
            'id'        // Local key on TeamEvent
        );
    }
    
    /**
     * @return HasMany<TeamEventTaskConfig, $this>
     */
    public function eventTaskConfigs(): HasMany {
        return $this->hasMany(TeamEventTaskConfig::class, 'team_id');
    }

    /**
     * @return HasMany<TeamStatsRange, $this>
     */
    public function activeStatsRanges(): HasMany {
        return $this->statsRanges()->active();
    }

    /**
     * @return HasMany<TeamStatsRange, $this>
     */
    public function statsRanges(): HasMany {
        return $this->hasMany(TeamStatsRange::class, 'team_id');
    }

    /**
     * @return HasMany<TeamRole, $this>
     */
    public function roles(): HasMany {
        return $this->hasMany(TeamRole::class, 'team_id');
    }

    /**
     * @return HasMany<TeamMember, $this>
     */
    public function membersWithRole(TeamRoleType $type): HasMany {
        return $this->members()->whereRelation('statusRole', 'name', $type);
    }

    /**
     * @return HasMany<TeamMember, $this>
     */
    public function managersWithAssociatedUser(): HasMany {
        return $this->membersWithRole(TeamRoleType::MANAGER)
            ->has('person.user');
    }

    /**
     * @return HasOne<TeamLedger, $this>
     */
    public function ledger(): HasOne {
        return $this->hasOne(TeamLedger::class, 'team_id');
    }
    
    public function getTimezone(): string {
        return "Europe/Berlin"; // Germany including DST
    }
}
