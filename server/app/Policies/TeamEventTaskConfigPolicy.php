<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\TeamEventTaskConfig;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamEventTaskConfigPolicy
{

    private function getTeamFromRequest(): Team {
        $id = request()->input('data.relationships.team.data.id');
        return Team::findOrFail($id);
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // is secured by global scope where each query contains only configs from teams of the current user
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamEventTaskConfig $teamEventTaskConfig): bool
    {
        // is secured by global scope where each query contains only configs from teams of the current user
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $team = $this->getTeamFromRequest();
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_CREATE, $team);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamEventTaskConfig $teamEventTaskConfig): bool
    {
        $team = $teamEventTaskConfig->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_CREATE, $team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamEventTaskConfig $teamEventTaskConfig): bool
    {
        $team = $teamEventTaskConfig->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_DELETE, $team);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamEventTaskConfig $teamEventTaskConfig): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamEventTaskConfig $teamEventTaskConfig): bool
    {
        return false;
    }
}
