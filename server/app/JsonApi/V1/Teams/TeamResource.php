<?php

namespace App\JsonApi\V1\Teams;

use App\Http\Controllers\AuthController;
use App\JsonApi\CommonRessource;
use App\Models\Team;
use App\Types\SubscriptionPriceType;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Laravel<PERSON>sonApi\Core\Resources\ConditionalFields;
use Laravel<PERSON>sonApi\Core\Resources\Relation;

/**
 * @extends CommonRessource<Team>
 */
class TeamResource extends CommonRessource {
    
    private function getTeam(): Team {
        return $this->resource;
    }

    /**
     * Get the resource's attributes.
     *
     * @param Request|null $request
     *
     * @return iterable<string, SubscriptionPriceType|bool|Carbon|int|string|null>
     */
    public function attributes($request): iterable {
        return [
            'name'                   => $this->getTeam()->name,
            'icon_name'              => $this->getTeam()->icon_name,
            'membersCount'           => $this->getTeam()->members()->count(),
            'registeredMembersCount' => $this->getTeam()->registeredMembers()->count(),
            'isSubscribed'           => $this->getTeam()->isSubscribed(),
            'subscriptionEndsAt'     => $this->getTeam()->getSubscriptionEndsAt(),
            'subscriptionPriceType'  => $this->getTeam()->getActiveSubscription()?->price_type,
            'isOnTrial'              => $this->getTeam()->isOnTrial(),
            'trialEndsAt'            => $this->getTeam()->trial_ends_at,
            'createdAt'              => $this->getTeam()->created_at,
            'updatedAt'              => $this->getTeam()->updated_at,
        ];
    }

    /**
     * Get the resource's relationships.
     *
     * @param Request|null $request
     *
     * @return iterable<Relation|ConditionalFields>
     */
    public function relationships($request): iterable {
        $user = AuthController::getCurrentUser();
        return [
            $this->relation('members'),
            $this->relation('events'),
            $this->relation('statsRanges'),
            $this->relation('activeStatsRanges'),
            $this->relation('roles'),
            $this->mergeWhen($this->isRelationRequested('invite') && $user && $user->can('view', $this->getTeam()->invite), [
                $this->relation('invite'),
            ]),
            $this->relation('ledger'),
            $this->relation('activeSubscription'),
        ];
    }

}
