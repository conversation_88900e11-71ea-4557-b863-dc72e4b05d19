<?php

namespace App\Mail;

use App\Models\Team;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class TeamCreatedWelcomeEmail extends Mailable
{
    use Queueable, SerializesModels;

    public string $featuresUrl;
    public string $teamUrl;
    public string $subscriptionUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public User $user,
        public Team $team
    ) {
        $this->featuresUrl = route('features');
        $this->teamUrl = config('app.frontend.url') . route(
                'frontend.team.detail',
                $team->id,
                absolute: false
            );
        $this->subscriptionUrl = config('app.frontend.url') . route(
                'frontend.team.subscription',
                $team->id,
                absolute: false
            );
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            replyTo: [(string)config('mail.hello')],
            subject: 'Willkommen bei Numo - Dein Team "' . $this->team->name . '" wurde erstellt',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.team-created-welcome',
            with: [
                'user' => $this->user,
                'team' => $this->team,
                'featuresUrl' => $this->featuresUrl,
                'teamUrl' => $this->teamUrl,
                'subscriptionUrl' => $this->subscriptionUrl,
                'appName' => config('app.name'),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
