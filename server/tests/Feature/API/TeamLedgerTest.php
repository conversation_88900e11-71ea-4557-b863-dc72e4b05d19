<?php

namespace API;

use App\Models\Team;
use App\Models\TeamLedgerClaim;
use App\Types\TeamLedgerClaimStatusType;
use App\Types\TeamRoleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Tests\Feature\API\JsonApiTestCase;
use function PHPUnit\Framework\assertEquals;
use function PHPUnit\Framework\assertFalse;
use function PHPUnit\Framework\assertTrue;

class TeamLedgerTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function test_listing_ledgers_is_forbidden(): void {
        $this->createUserActingAs();
        $this->jsonApi('teamLedgers')
            ->expects('teamLedgers')
            ->get(route('api.v1.teamLedgers.index'))
            ->assertForbidden();
    }
    
    public function test_getting_ledgers_from_team(): void {
        $teamOK = $this->createTeam('visible');
        $user = $this->createUserActingAs();
        $teamOK->createMember(user: $user);
        $this->jsonApi('team')
            ->expects('teams')
            ->includePaths('ledger')
            ->get(route('api.v1.teams.index'))
            ->assertOk()
            ->assertIsIncluded('teamLedgers', $teamOK->ledger->id);
    }
    
    public function test_ledger_is_visible_only_when_user_is_member(): void {
        $teamOK = $this->createTeam('visible');
        $teamNOK = $this->createTeam('not visible');
        $ledgerNotVisibleId = $teamNOK->ledger->id;
        $user = $this->createUserActingAs();
        $teamOK->createMember(user: $user);
        
        $this->jsonApi('teamLedgers')
            ->get(route('api.v1.teamLedgers.show', [
                'teamLedger' => $teamOK->ledger->id,
            ]))
            ->assertOk()
            ->assertFetchedOne($teamOK->ledger)
        ;
        
        $this->jsonApi('teamLedgers')
            ->get(route('api.v1.teamLedgers.show', [
                'teamLedger' => $ledgerNotVisibleId,
            ]))
            ->assertNotFound()
        ;
    }
    
    public function test_ledger_can_be_created_only_once(): void {
        $teamOK = $this->createTeam('visible', false);
        $teamOKData = $this->createNewLedgerData($teamOK);
        $user = $this->createUserActingAs();
        $teamOK->createMember(teamRoleType: TeamRoleType::TREASURER, user: $user);
        
        $this->jsonApi('teamLedgers')
            ->withData($teamOKData)
            ->post(route('api.v1.teamLedgers.store'))
            ->assertCreated()
        ;
        
        $this->jsonApi('teamLedgers')
            ->withData($teamOKData)
            ->post(route('api.v1.teamLedgers.store'))
            ->isServerError()
        ;
    }
    
    public function test_ledger_can_be_created_only_with_permission_and_when_user_is_member(): void {
        $teamOKPermissions = $this->createTeam('visible', false);
        $teamOKPermissionData = $this->createNewLedgerData($teamOKPermissions);
        
        $teamOKNoPermissions = $this->createTeam('visible no permissions', false);
        $teamOKNoPermissionData = $this->createNewLedgerData($teamOKNoPermissions);
        
        $teamNOK = $this->createTeam('not visible', false);
        $teamNOKData = $this->createNewLedgerData($teamNOK);
        
        $user = $this->createUserActingAs();
        $teamOKPermissions->createMember(teamRoleType: TeamRoleType::TREASURER, user: $user);
        $teamOKNoPermissions->createMember(user: $user);
        
        $this->jsonApi('teamLedgers')
            ->withData($teamOKPermissionData)
            ->post(route('api.v1.teamLedgers.store'))
            ->assertCreated()
        ;
        
        $this->jsonApi('teamLedgers')
            ->withData($teamOKNoPermissionData)
            ->post(route('api.v1.teamLedgers.store'))
            ->assertForbidden()
        ;
        
        $this->jsonApi('teamLedgers')
            ->withData($teamNOKData)
            ->post(route('api.v1.teamLedgers.store'))
            ->assertNotFound()
        ;
    }
    
    public function test_team_create_has_no_ledger_and_sets_treasurer_role_to_manager (): void {
        $user = $this->createUser();
        $data = $this->createNewTeamData();

        $teamId = $this->actingAs($user)
            ->jsonApi('teams')
            ->withData($data)
            ->post(route('api.v1.teams.store'))
            ->assertCreated()
            ->id()
        ;

        $team = Team::find($teamId);
        self::assertNull($team->ledger);
        self::assertTrue($team->members->first()->roles->contains($team->getTeamRole(TeamRoleType::TREASURER)));
    }
    
    public function test_credit_balance_is_visible_only_when_user_is_member(): void {
        $teamOK = $this->createTeam('visible');
        $teamNOK = $this->createTeam('not visible');
        $ledgerNotVisibleId = $teamNOK->ledger->id;
        $user = $this->createUserActingAs();
        $teamOK->createMember(user: $user);

        $this->jsonApi('teamLedgers')
            ->get(route('api.v1.teamLedgers.show', [
                'teamLedger' => $teamOK->ledger->id,
            ]))
            ->assertOk()
            ->assertFetchedOne($teamOK->ledger)
        ;

        $this->jsonApi('teamLedgers')
            ->get(route('api.v1.teamLedgers.show', [
                'teamLedger' => $ledgerNotVisibleId,
            ]))
            ->assertNotFound()
        ;
    }
    
    public function test_soft_delete_fine_or_due_still_loads_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount = money(10);
        $fine = $team->ledger->fines()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $due = $team->ledger->dues()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $claimFine = $fine->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(),]);
        $claimDue = $due->claims()->first();

        assertEquals($amount->multiply(2), $team->ledger->getClaimBalance());

        $fine->delete();
        $due->delete();
        
        self::assertCount(2, TeamLedgerClaim::with('claimable')->whereTeamLedgerId($team->ledger->id)->get());

        $this->actingAs($user)
            ->jsonApi('teamLedgerClaims')
            ->includePaths('claimable')
            ->filter([
                'ledgerId' => $team->ledger->id,
                'claimableType' => 'teamLedgerFines',
            ])
            ->get(route('api.v1.teamLedgerClaims.index'))
            ->assertFetchedMany([$claimFine])
            ->assertIsIncluded('teamLedgerFines', $fine)
        ;
        
        $this->actingAs($user)
            ->jsonApi('teamLedgerClaims')
            ->includePaths('claimable')
            ->filter([
                'ledgerId' => $team->ledger->id,
                'claimableType' => 'teamLedgerDues',
            ])
            ->get(route('api.v1.teamLedgerClaims.index'))
            ->assertFetchedMany([$claimDue])
            ->assertIsIncluded('teamLedgerDues', $due)
        ;
    }

    public function test_credit_balance_from_api_can_only_viewed_for_team_members(): void {
        $teamAllowed = $this->createTeam('Allowed');
        $teamForbidden = $this->createTeam('Forbidden');
        $user = $this->createUserActingAs();
        $member = $teamAllowed->createMember(user: $user);

        $this->get(route('api.teamLedger.getCreditBalance', [
            'ledger' => $teamAllowed->ledger->id,
        ]))->assertOk();

        $this->get(route('api.teamLedger.getCreditBalance', [
            'ledger' => $teamForbidden->ledger->id,
        ]))->assertForbidden();
    }
    
    public function test_credit_balance_from_api(): void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountMember = money(10);
        $amountNonMember = money(20);
        $team->ledger->transactions()->create(['team_member_id' => $member->id, 'amount' => $amountMember, 'title' => 'paid',]);
        $team->ledger->transactions()->create(['amount' => $amountNonMember, 'title' => 'paid',]);
        
        $this->get(route('api.teamLedger.getCreditBalance', [
            'ledger' => $team->ledger->id,
        ]))->assertJson(['amount' => $amountMember->getAmount()]); // non members cannot have credit, this is just a deposit and thus does not appear in ledger credit balance
        
        $this->get(route('api.teamLedger.getCreditBalance', [
            'ledger' => $team->ledger->id,
            'member' => $member->id,
        ]))->assertJson(['amount' => $amountMember->getAmount()]);
    }


    public function test_claim_status_on_api_can_only_updated_by_treasurers_for_own_team(): void {
        $teamAllowed = $this->createTeam('Allowed');
        $teamForbidden = $this->createTeam('Forbidden');
        $user = $this->createUserActingAs();
        $memberAllowed = $teamAllowed->createMember(user: $user);
        
        $memberForbidden = $teamForbidden->createMember();

        $fineAllowed = $teamAllowed->ledger->fines()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => money(10)]);
        $claimAllowed = $fineAllowed->claims()->create(['team_member_id' => $memberAllowed->id, 'due_date' => Carbon::now(),]);
        
        $fineForbidden = $teamForbidden->ledger->fines()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => money(10)]);
        $claimForbidden = $fineForbidden->claims()->create(['team_member_id' => $memberForbidden->id, 'due_date' => Carbon::now(),]);
        
        
        $this->post(route('api.teamLedger.setClaimStatus', [
            'claim' => $claimAllowed->id,
            'status' => TeamLedgerClaimStatusType::FULFILLED
        ]))->assertForbidden();

        $memberAllowed->roles()->attach($teamAllowed->getTeamRole(TeamRoleType::TREASURER));
        $user->refresh();
        $this->post(route('api.teamLedger.setClaimStatus', [
            'claim' => $claimAllowed->id,
            'status' => TeamLedgerClaimStatusType::FULFILLED
        ]))->assertOk();
        
        $claimAllowed->refresh();
        self::assertTrue($claimAllowed->isFulfilled());


        $this->post(route('api.teamLedger.setClaimStatus', [
            'claim' => $claimForbidden->id,
            'status' => TeamLedgerClaimStatusType::FULFILLED
        ]))->assertForbidden();

        $claimForbidden->refresh();
        self::assertFalse($claimForbidden->isFulfilled());
    }
    
    public function test_set_claim_status_can_create_transaction(): void {
        $team = $this->createTeam('Allowed');
        $user = $this->createUserActingAs();
        $member = $team->createMember(teamRoleType: TeamRoleType::TREASURER, user: $user);

        $amount = money(10);
        $fine = $team->ledger->fines()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $claim = $fine->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(),]);

        $this->post(route('api.teamLedger.setClaimStatus', [
            'claim' => $claim->id,
            'status' => TeamLedgerClaimStatusType::EXEMPT
        ]), [
                'createTransactionAmount' => $amount->getAmount()
            ]
        )->assertOk();
        
        $claim->refresh();
        assertTrue($claim->exempt);
        assertFalse($claim->isFulfilled());
        assertEquals($amount, $team->ledger->transactions()->first()->amount);
    }
    
    public function test_set_claim_status_unfulfilled_leaves_fulfills_from_credit(): void {
        $team = $this->createTeam('Allowed');
        $user = $this->createUserActingAs();
        $member = $team->createMember(teamRoleType: TeamRoleType::TREASURER, user: $user);

        $amount = money(10);
        $fine = $team->ledger->fines()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $claim1 = $fine->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->addDay(),]);
        $claim2 = $fine->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(),]);
        $claim1->setFulfilled();
        $team->ledger->transactions()->create(['team_member_id' => $member->id, 'amount' => $amount, 'title' => 'paid',]);
        
        assertEquals(money(0), $team->ledger->getCreditBalance($member));
        
        $this->post(route('api.teamLedger.setClaimStatus', [
            'claim' => $claim1->id,
            'status' => TeamLedgerClaimStatusType::UNFULFILLED
        ]))->assertOk();
        
        $claim1->refresh();
        $claim2->refresh();
        assertFalse($claim1->isFulfilled());
        assertTrue($claim2->isFulfilled());
        assertEquals(money(0), $team->ledger->getCreditBalance($member));
    }
}
