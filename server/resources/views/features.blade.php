<x-main-layout-with-menu>

    <section class="banner section-blue">
        <div class="banner-overlay">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="banner-text">
                            <h2>Entdecke alle Funktionen von numo</h2>
                            <p class="lead banner-text">Wir wollen die Organisation im Verein verbessern, Lust auf Ehrenamt schaffen und Mitarbeit aktiv fördern.<br>
                                Wir sind noch lange nicht am Ziel, aber schon jetzt bietet numo eine große Organisationshilfe für deine Gruppe.</p>
                            <div class="text-center mb-3">
                                <a href="https://meetings-eu1.hubspot.com/thomas-klaas" target="_blank" class="btn btn-light btn-lg mr-3">
                                    <i class="fa fa-video"></i> 15min Live Demo buchen
                                </a>
                                <span class="badge badge-success">Vereinsrabatt 20-50%</span>
                            </div>
                            <x-call-to-action-button/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center" id="terminliste">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Terminliste - Behalte den Überblick</h5>
                                <span class="line"></span>
                                <p class="card-text">
                                    Egal ob ihr numo nur für euch selbst oder für die ganze Familie nutzt, die Terminliste passt sich an und zeigt euch immer eine komplette Übersicht.
                                    So geht kein Termin verloren und auf einem Blick sieht man, ob der Trainer schon eine Rückmeldung bekommen hat.
                                </p>
                                <ul class="nav nav-tabs card-header-tabs nav-fill flex-column flex-md-row">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-toggle="tab" href="#feature-join-team" role="tab" aria-controls="feature-join-team" aria-selected="true">Team beitreten</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-terminliste-1-1" role="tab" aria-controls="feature-terminliste-1-1" aria-selected="true">1 Team / 1 Person</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-terminliste-1-2" role="tab" aria-controls="feature-terminliste-1-2" aria-selected="false">1 Team / 2 Personen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-terminliste-2-1" role="tab" aria-controls="feature-terminliste-2-1" aria-selected="false">2 Teams / je 1 Person</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-terminliste-2-2" role="tab" aria-controls="feature-terminliste-2-2" aria-selected="false">2 Teams / 1 und 2 Personen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-terminliste-2-3" role="tab" aria-controls="feature-terminliste-2-3" aria-selected="false">2 Teams / 2 und 3 Personen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-i-cal" role="tab" aria-controls="feature-i-cal" aria-selected="false">Abonnieren / iCal</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body section-blue d-flex justify-content-center text-center">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="feature-join-team" role="tabpanel" aria-labelledby="feature-join-team-tab">
                                        <p>Beim beitreten könnt ihr beliebig viele Teammitglieder auswählen oder hinzufügen,
                                            die von euch verwaltet werden.
                                        </p>
                                        <p>Bei zwei oder mehr Kindern in einer Gruppe, kann man diese direkt alle anlegen und im Anschluss an einem Termin individuell abstimmen und kommentieren.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/team-beitreten.webm')}}"/>
                                    </div>
                                    <div class="tab-pane fade show" id="feature-terminliste-1-1" role="tabpanel" aria-labelledby="feature-terminliste-1-1-tab">
                                        <p>Reduziert auf das nötigste, für dich alleine oder dein Kind.</p>
                                        <x-iphone-image-container src="{{Vite::image('features/terminliste-1-1.png')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-terminliste-1-2" role="tabpanel" aria-labelledby="feature-terminliste-1-2-tab">
                                        <p>Ab 2 Personen wird der Name mit angezeigt.</p>
                                        <x-iphone-image-container src="{{Vite::image('features/terminliste-1-2.png')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-terminliste-2-1" role="tabpanel" aria-labelledby="feature-terminliste-2-1-tab">
                                        <p>Ab 2 Teams wird der Teamname über jedem Termin angezeigt und auch der Name der Person im Team.</p>
                                        <x-iphone-image-container src="{{Vite::image('features/terminliste-2-1.png')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-terminliste-2-2" role="tabpanel" aria-labelledby="feature-terminliste-2-2-tab">
                                        <p>Egal ob 1 oder 2 Personen im Team sind, mit den Teamnamen und Personen Namen verlierst du nie den Überblick.</p>
                                        <x-iphone-image-container src="{{Vite::image('features/terminliste-2-2.png')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-terminliste-2-3" role="tabpanel" aria-labelledby="feature-terminliste-2-3-tab">
                                        <p>Bei der Anzahl Personen im Team, die von dir verwaltet werden, gibt es kein Limit. Auch bei 3 oder mehr Personen bleibt es übersichtlich.</p>
                                        <x-iphone-image-container src="{{Vite::image('features/terminliste-2-3.png')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-i-cal" role="tabpanel" aria-labelledby="feature-i-cal">
                                        <p>Termine könnt ihr auch ganz einfach in eurem Kalender abonnieren.</p>
                                        <p>Entweder für jedes Familienmitglied einzeln, oder direkt alle über einen Link.<br>
                                                Einmal abonniert, könnt ihr die Einstellungen jederzeit in Numo anpassen</p>
                                        <x-iphone-video-container src="{{Vite::video('features/i-cal.webm')}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>


    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center" id="termine-verwalten">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Trainer: Termine verwalten</h5>
                                <span class="line"></span>
                                <p class="card-text">
                                    Rückmeldungen über WhatsApp, im Supermarkt oder sonst irgendwo persönlich? Öffnet schnell die App und tragt alles ein.<br>
                                    Genauso schnell könnt ihr auch Termine hinzufügen, kopieren, absagen oder löschen
                                </p>
                                <ul class="nav nav-tabs card-header-tabs nav-fill flex-column flex-md-row">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-toggle="tab" href="#feature-vote-other" role="tab" aria-controls="feature-vote-other" aria-selected="true">Zu-/Absagen eintragen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-serientermin-erstellen" role="tab" aria-controls="feature-serientermin-erstellen" aria-selected="true">Serientermin erstellen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-wettkampf-erstellen" role="tab" aria-controls="feature-wettkampf-erstellen" aria-selected="false">Wettkampf erstellen + Direktlink</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-termin-kopieren" role="tab" aria-controls="feature-termin-kopieren" aria-selected="false">Termin kopieren</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-termin-absagen" role="tab" aria-controls="feature-termin-absagen" aria-selected="false">Termin absagen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-serientermin-loeschen" role="tab" aria-controls="feature-serientermin-loeschen" aria-selected="false">Serientermin löschen</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body section-blue d-flex justify-content-center text-center">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="feature-vote-other" role="tabpanel" aria-labelledby="feature-vote-other-tab">
                                        <p>Mit Numo könnt ihr als Trainer alle Infos direkt am Termin eintragen und alle Trainer kennen jederzeit den gleichen Stand.<br>
                                            Links: Trainer trägt Infos ein<br>
                                            Rechts: beim zweiten Trainer aktualisiert sich die Liste automatisch wenn diese wieder aufgerufen wird
                                        </p>
                                        <div class="row">
                                            <div class="iphone-container">
                                                <div style="height: 640px; width: 310px; overflow: hidden; position: relative; margin-left: 20px">
                                                    <video src="{{Vite::video('features/vote-other.webm')}}" loop autoplay muted preload="auto" type="video/webm"
                                                           style="height: 570px; width: auto; top: 72px; left: 0"/>
                                                </div>
                                            </div>
                                            <div class="iphone-container">
                                                <div style="height: 640px; width: 310px; overflow: hidden; position: relative; margin-left: 20px">
                                                    <video src="{{Vite::video('features/vote-other.webm')}}" loop autoplay muted preload="auto" type="video/webm"
                                                           style="height: 570px; width: auto; top: 72px; left: 0; margin-left: -383px"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade show" id="feature-serientermin-erstellen" role="tabpanel" aria-labelledby="feature-serientermin-erstellen-tab">
                                        <p>Serientermine erstellen für einen Wochentag oder gleich mehrere?<br>
                                            Für wechselnde Tage im 2-Wochen Rhythmus?<br>
                                            Oder an jedem ersten Montag im Monat?<br>
                                            Mit numo ist das alles schnell und unkompliziert erledigt..</p>
                                        <x-iphone-video-container src="{{Vite::video('features/serientermin-erstellen-und-notiz-trainer.webm')}}"/>
                                        <p>Ändert sich doch was an einem einzelnen Termin? Kein Problem, jeder Termin lässt sich einzeln bearbeiten.</p>
                                        <p>Wichtige Infos für die Trainer laufen noch über WhatsApp und jeder muss es sich dann bis zum Termin merken? Mit numo könnt ihr Notizen zu jedem Termin hinzufügen, die nur für die Trainer sichtbar sind. So sind alle immer auf dem gleichen Stand, die Infos sind genau dann sichtbar wenn die benötigt werden und es geht nichts im Chatverlauf unter.</p>
                                    </div>
                                    <div class="tab-pane fade" id="feature-wettkampf-erstellen" role="tabpanel" aria-labelledby="feature-wettkampf-erstellen-tab">
                                        <p>Beim Wettkampf gibt es extra Felder für Heim / Auswärts und den Gegner.<br>
                                            Bei jeder Terminart kann eine Adresse eingetragen werden, mit der sich google Maps öffnen lässt.</p>
                                        <p>Vergessen deine Spieler schon mal abzusagen? Dann stell die Rückmeldung so ein, dass alle zusagen müssen.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/wettkampf-erstellen.webm')}}"/>
                                        <p>Auf der Termin Detail Seite kannst du direkt einen Link für eure WhatsApp Gruppe kopieren. So kommen die Spieler direkt auf die richtige Seite um mit einem Klick auf den Daumen die Rückmeldungen einzutragen. So sehen alle Trainer und Spieler immer den aktuellen Stand der Zusagen.</p>
                                    </div>
                                    <div class="tab-pane fade" id="feature-termin-kopieren" role="tabpanel" aria-labelledby="feature-termin-kopieren-tab">
                                        <p>Das nächste Heimspiel steht an? Einfach das letzte Spiel kopieren, Gegner und Datum ändern und schon können eure Spieler abstimmen.</p>
                                        <p>Ihr habt das Training nur bis zu den Osterferien eingetragen? Mit wenigen Schritten kopiert ihr ein beliebiges Training, wählt das Startdatum, legt eine wöchentliche Wiederholung für zwei Wochentage und das Enddatum fest und schon stehen alle Termine bis zu den Sommerferien drin.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/termin-kopieren.webm')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-termin-absagen" role="tabpanel" aria-labelledby="feature-termin-absagen-tab">
                                        <p>Das Training fällt aus, also einfach den Termin löschen? <br>Eine bessere Möglichkeit ist es, den Termin abzusagen.</p>
                                        <p>Der Vorteil der Absage eines Termins gegenüber einer Löschung: Falls der Termin doch stattfindet, kannst du diesen wieder aktivieren und musst die Daten nicht neu einpflegen. Außerdem bleibt der Termin in der Übersicht in Rot stehen, so ist es für jeden gut sichtbar.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/termin-absagen.webm')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-serientermin-loeschen" role="tabpanel" aria-labelledby="feature-serientermin-loeschen-tab">
                                        <p>Ihr ändert euren Trainingstag oder geht früher in die Sommerpause?<br>
                                            Mit numo könnt ihr schnell alle zukünftigen Termine einer Terminserie löschen.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/serientermin-loeschen.webm')}}"/>
                                        <p>Ihr wollt das Training auf einen anderen Tag legen? Einfach alle zukünftigen Termine löschen,<br>
                                            Termin kopieren und daraus direkt eine neue Terminserie für beliebig viele Wochentage anlegen.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>


    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center" id="abwesenheit">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Abwesenheit eintragen</h5>
                                <span class="line"></span>
                                <p class="card-text">
                                    Ob Urlaub, Krankheit oder Inaktiv, egal in wie vielen Gruppen ihr selbst oder eure Kinder sind: Mit einem Eintrag wissen alle Trainer und Gruppen Bescheid.<br>
                                    Wann ihr wieder da seid, ist an jedem Termin für eure Trainer als Kommentar sichtbar, so bleiben keine Fragen offen.
                                </p>
                                <ul class="nav nav-tabs card-header-tabs nav-fill flex-column flex-md-row">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-toggle="tab" href="#feature-abwesenheit-urlaub" role="tab" aria-controls="feature-abwesenheit-urlaub" aria-selected="true">Urlaub</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-abwesenheit-1-tag-pro-woche" role="tab" aria-controls="feature-abwesenheit-1-tag-pro-woche" aria-selected="true">1 Tag / Woche</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-abwesenheit-termin-ueberschneidung" role="tab" aria-controls="feature-abwesenheit-termin-ueberschneidung" aria-selected="false">Termin Überschneidung</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body section-blue d-flex justify-content-center text-center">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="feature-abwesenheit-urlaub" role="tabpanel" aria-labelledby="feature-abwesenheit-urlaub-tab">
                                        <p>Ihr fahrt mit der ganzen Familie in Urlaub oder seid krank? Da gibt es sicher einiges an Vorbereitung und Dinge an die ihr denken müsst. <br>
                                            Oft wird dann vergessen beim Trainer oder in der App für jedes Kind und jede Gruppe abzusagen.</p>
                                        <p>Mit numo könnt ihr euren Familienurlaub genießen.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/abwesenheit-urlaub.webm')}}"/>
                                    </div>
                                    <div class="tab-pane fade show" id="feature-abwesenheit-1-tag-pro-woche" role="tabpanel" aria-labelledby="feature-abwesenheit-1-tag-pro-woche-tab">
                                        <p>Euer Kind hat an einem Tag in der Woche regelmäßig einen anderen Termin und kann nicht zum Training? Mit numo könnt ihr das als Abwesenheit eintragen und braucht nicht mehr dran denken bei eurem Trainer abzusagen.</p>
                                        <p>Entweder unbegrenzt, oder direkt mit Enddatum.<br>
                                            Natürlich könnt ihr das Enddatum auch später noch hinzufügen oder den Wochentag anpassen.<br>
                                            Die Änderungen werden dann nur auf zukünftige Termine angewendet</p>
                                        <x-iphone-video-container src="{{Vite::video('features/abwesenheit-1-tag-pro-woche.webm')}}"/>
                                    </div>
                                    <div class="tab-pane fade" id="feature-abwesenheit-termin-ueberschneidung" role="tabpanel" aria-labelledby="feature-abwesenheit-termin-ueberschneidung-tab">
                                        <p>Ihr seid in mehreren Gruppen aktiv und die Trainingstage überschneiden sich? In unserem Beispiel:<br>
                                            Fußball ist Mo + Mi<br>
                                            Kinderturnen Mi + Fr.<br>
                                            Badminton nur Mi.</p>
                                        <p>Tragt für Fußball und Kinderturnen einfach eine regelmäßige Abwesenheit für Mittwochs ein, so wissen alle Trainer und eure Mitspieler immer Bescheid, wann ihr da seid.<br>
                                            Natürlich könnt ihr die Zusagen für einzelne Tage immer noch anpassen wenn Badminton ausfällt, oder ihr doch mal mehr Lust auf Fußball habt.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/abwesenheit-termin-ueberschneidung.webm')}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>

    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center">
                        <div class="card">
                            <div class="card-body section-blue justify-content-center text-center">
                                <h5 class="card-title">Starte noch heute mit deinem Team!</h5>
                                <span class="line"></span>
                                <p class="mb-3">Buche eine kostenlose 15-minütige Live Demo und erfahre mehr über unseren Vereinsrabatt!</p>
                                <div class="mb-3">
                                    <a href="https://meetings-eu1.hubspot.com/thomas-klaas" target="_blank" class="btn btn-light btn-lg">
                                        <i class="fa fa-video"></i> Kostenlose Live Demo
                                    </a>
                                </div>
                                <x-call-to-action-button/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>


    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center" id="aufgaben">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Aufgaben - neu gedacht</h5>
                                <span class="line"></span>
                                <p class="card-text">
                                    Aufgaben zu einem Termin erstellen ist eine Sache. Zu wissen, wer die Aufgabe übernehmen kann, ist die eigentliche Frage.<br>
                                    Mit numo wird das zum Kinderspiel!
                                </p>
                                <ul class="nav nav-tabs card-header-tabs nav-fill flex-column flex-md-row">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-toggle="tab" href="#feature-aufgaben-erstellen" role="tab" aria-controls="feature-aufgaben-erstellen" aria-selected="true">Aufgaben erstellen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-aufgaben-inline-statistik" role="tab" aria-controls="feature-aufgaben-inline-statistik" aria-selected="true">Aufgaben zuordnen</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body section-blue d-flex justify-content-center text-center">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="feature-aufgaben-erstellen" role="tabpanel" aria-labelledby="feature-aufgaben-erstellen-tab">
                                        <p>Ihr habt Aufgaben für euren Spieltag? Mit numo könnt ihr unkompliziert Aufgaben erstellen und zuweisen.<br>
                                            Oder ihr lasst es einfach offen und jeder kann die Aufgabe direkt "übernehmen".
                                        </p>
                                        <x-iphone-video-container src="{{Vite::video('features/aufgaben-erstellen.webm')}}"/>
                                    </div>
                                    <div class="tab-pane fade show" id="feature-aufgaben-inline-statistik" role="tabpanel" aria-labelledby="feature-aufgaben-inline-statistik-tab">
                                        <p>Bei euch werden die Trikots von jedem einmal gewaschen?<br>
                                            Mit numo habt ihr alle Informationen zur Entscheidungsfindung direkt am Spieltag griffbereit.</p>
                                        <p>Auf einem Blick im Termin seht ihr alle Infos:</p>
                                        <ul class="text-left pl-4">
                                            <li>wer wie oft dran war</li>
                                            <li>wer als nächstes dran sein kann UND ob die Person beim Termin auch dabei ist</li>
                                            <li>wer die Trikots beim letzten mal hatte und somit heute mitbringen muss</li>
                                        </ul>
                                        <x-iphone-video-container src="{{Vite::video('features/aufgaben-zuordnen.webm')}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>


    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center" id="statistik">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Statistik - wer, wann, wo</h5>
                                <span class="line"></span>
                                <p class="card-text">
                                    Bei vielen Trainings und Wettkämpfen mit jeweils zugehörigen Aufgaben fallen eine ganze Menge Informationen an.<br>
                                    Mit numo verlierst du nie den Überblick!
                                </p>
                                <ul class="nav nav-tabs card-header-tabs nav-fill flex-column flex-md-row">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-toggle="tab" href="#feature-statistik-anzeigen" role="tab" aria-controls="feature-statistik-anzeigen" aria-selected="true">Statistik ansehen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-statistik-zeitraum" role="tab" aria-controls="feature-statistik-zeitraum" aria-selected="true">Zeitraum erstellen</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body section-blue d-flex justify-content-center text-center">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="feature-statistik-anzeigen" role="tabpanel" aria-labelledby="feature-statistik-anzeigen-tab">
                                        <p>Einfach dein Team auswählen und schon hast du Zugriff auf alle Daten über jeden beliebigen Zeitraum.</p>
                                        <p>Im Vollbildmodus mit feststehenden Namen ist selbst auf einem kleinen Bildschirm eine große Menge sichtbar und immer griffbereit.</p>
                                        <x-iphone-video-container src="{{Vite::video('features/statistik-anzeigen.webm')}}"/>
                                    </div>
                                    <div class="tab-pane fade show" id="feature-statistik-zeitraum" role="tabpanel" aria-labelledby="feature-statistik-zeitraum-tab">
                                        <p>Ihr wollt eure Statistik immer direkt für die laufende Saison angucken? Und auch bei den Aufgaben im Termin soll die laufende Saison betrachtet werden? Dann erstellt einfach einen Zeitraum.<br>
                                        Alles weitere übernimmt numo für euch:</p>
                                        <ul class="text-left pl-3">
                                            <li>Wenn das heutige Datum in einem vordefinierten Zeitraum liegt, wird dieser automatisch ausgewählt.</li>
                                            <li>Bei den Aufgaben Statistiken innerhalb von einem Termin wird automatisch der zum Termin passende Zeitraum betrachtet.</li>
                                            <li>Gibt es mehrere Zeiträume zu einem Tag, hat immer der mit dem spätesten Enddatum Vorrang.</li>
                                        </ul>
                                        <x-iphone-video-container src="{{Vite::video('features/statistik-zeitraum.webm')}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>
    
    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center" id="teamkasse">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Teamkasse</h5>
                                <span class="line"></span>
                                <p class="card-text">
                                    Egal ob Strafen, einmalige oder regelmäßige Beiträge, Geld Ein- und Ausgänge,<br>
                                    Guthaben oder Spenden in die Kasse.<br>
                                    Mit numo bist du perfekt organisiert, so will jeder die Kasse übernehmen!
                                </p>
                                <p>Die Kasse in numo funktioniert genau so, wie du es auf dem Platz brauchst:<br>
                                    Die Spieler fragen was offen ist, runden auf den nächsten 10er auf und der Rest verbleibt in der Kasse.<br>
                                    <strong>Mit numo ist das kein Problem:</strong><br>
                                    Ein Geldeingang markiert die offenen Strafen oder Beiträge automatisch als erfüllt und bucht den Rest als Guthaben.<br>
                                    Natürlich kannst du auch einfach eine Strafe als "Erfüllt" markieren und der entsprechende Betrag wird verbucht.
                                </p>
                                <ul class="nav nav-tabs card-header-tabs nav-fill flex-column flex-md-row">
                                    <li class="nav-item">
                                        <a class="nav-link active" data-toggle="tab" href="#feature-teamkasse-uebersicht" role="tab" aria-controls="feature-teamkasse-uebersicht" aria-selected="true">Übersicht</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-teamkasse-mitglieder" role="tab" aria-controls="feature-teamkasse-mitglieder" aria-selected="true">Mitglieder Übersicht</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-teamkasse-strafen" role="tab" aria-controls="feature-teamkasse-strafen" aria-selected="true">Strafen</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-teamkasse-beitraege" role="tab" aria-controls="feature-teamkasse-beitraege" aria-selected="true">Beiträge</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-teamkasse-strafenkatalog" role="tab" aria-controls="feature-teamkasse-strafenkatalog" aria-selected="true">Strafenkatalog</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-teamkasse-beitragskatalog" role="tab" aria-controls="feature-teamkasse-beitragskatalog" aria-selected="true">Beitragskatalog</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" data-toggle="tab" href="#feature-teamkasse-umsaetze" role="tab" aria-controls="feature-teamkasse-umsaetze" aria-selected="true">Umsätze</a>
                                    </li>
                                    
{{--                                    TODO: Besonderheiten im extra Tab?
--}}
                                </ul>
                            </div>
                            <div class="card-body section-blue d-flex justify-content-center text-center">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="feature-teamkasse-uebersicht" role="tabpanel" aria-labelledby="feature-teamkasse-uebersicht-tab">
                                        <p>Die wichtigsten Zahlen immer im Blick.</p>
                                        <p>Wie viel Geld ist gerade in der Kasse? Wie viel davon ist verfügbar und was ist Guthaben? Welche Forderungen (Geld oder Gegenstände) sind noch offen? Wie viel Geld kommt in Zukunft über die Beiträge noch rein?</p>
{{--                                        TODO: Bild / Video von Balance Overview--}}
{{--                                        <x-iphone-video-container src="{{Vite::video('features/statistik-anzeigen.webm')}}"/>--}}
                                    </div>
                                    <div class="tab-pane fade show" id="feature-teamkasse-mitglieder" role="tabpanel" aria-labelledby="feature-teamkasse-mitglieder-tab">
                                        <p>Das Herzstück der Kasse: Die Übersicht für jedes Teammitglied</p>
                                        <ul class="text-left pl-3">
                                            <li>Welche Beträge sind jetzt oder zukünftig fällig, wie viel wurde bisher eingezahlt und gibt es Guthaben?</li>
                                            <li>Neue Ein- und Auszahlungen erstellen (für ein Mitglied oder mit Verwendungszweck wenn ihr eine Spende bekommt oder den Deckel bezahlt).</li>
                                            <li>Strafen mit wenigen Klicks direkt zuordnen.</li>
                                            <li>Welche Gegenstände (z.b. Kuchen oder Kasten) sind noch fällig?</li>
                                            <li>Das Beste: Ein Geldeingang markiert die offenen Strafen oder Beiträge automatisch als erfüllt und bucht den Rest als Guthaben.</li>
                                            <li>Ehemalige Mitglieder werden weiterhin angezeigt, wenn es bereits Ein- oder Auszahlungen gab oder Strafen / Beiträge zugeordnet wurden.</li>
                                        </ul>
                                        {{--                                        TODO: Bild / Video von Member Overview inkl. Gegenstände--}}
{{--                                        <x-iphone-video-container src="{{Vite::video('features/statistik-zeitraum.webm')}}"/>--}}
                                    </div>
                                    <div class="tab-pane fade show" id="feature-teamkasse-strafen" role="tabpanel" aria-labelledby="feature-teamkasse-strafen-tab">
                                        <p>Hier kannst du Strafen als Erfüllt markieren, wieder zurücknehmen oder Das Mitglied davon befreien.<br>
                                        Egal was du änderst, numo kümmert sich um alles weitere und zeigt dir vorher immer genau was passiert.
                                        </p>
                                        <p><strong>Bevor du hier Einträge findest, musst du im Strafenkatalog Strafen erstellen und dann einem Mitglied zuweisen.</strong></p>
                                        <ul class="text-left pl-3">
                                            <li>Eine Strafe wurde schon bezahlt, nachträglich wird aber entschieden, dass die Person davon befreit wird? Du kannst direkt angeben, ob der Betrag als Guthaben verbleibt oder ausbezahlt wird.</li>
                                            <li>Eine Strafe wird gelöscht? Der Betrag verbleibt automatisch als Guthaben in der Kasse.</li>
                                            <li>Der Tag der Strafe ändert sich aber letzte Woche wurde der Betrag für die Strafe angepasst? Kein Problem, numo nimmt immer den zum Fälligkeitsdatum gültigen Betrag.
                                                <ul class="pl-4">
                                                    <li>Der neue Betrag ist höher? Wenn Guthaben vorhanden ist, wird es verwendet. Ansonsten wird die Strafe als nicht erfüllt markiert und der aktuelle Betrag als Guthaben notiert.</li>
                                                    <li>Der neue Betrag ist niedriger? Die Differenz wird direkt als Guthaben notiert.</li>
                                                </ul>
                                            </li>
                                            <li>Wann immer Guthaben bei einer Änderung entsteht, wird versucht andere offenen Strafen oder Beiträge davon zu begleichen.</li>
                                        </ul>
                                        
                                        {{--                                        TODO: Bild / Video von Balance Overview--}}
                                        {{--                                        <x-iphone-video-container src="{{Vite::video('features/statistik-anzeigen.webm')}}"/>--}}
                                    </div>
                                    <div class="tab-pane fade show" id="feature-teamkasse-beitraege" role="tabpanel" aria-labelledby="feature-teamkasse-beitraege-tab">
                                        <p>Hier kannst du Beiträge als Erfüllt markieren, wieder zurücknehmen oder Das Mitglied davon befreien.<br>
                                        Egal was du änderst, numo kümmert sich um alles weitere und zeigt dir vorher immer genau was passiert.
                                        </p>
                                        <p><strong>Bevor du hier Einträge findest, musst du oberhalb der Liste oder im Beitragskatalog einmalige oder regelmäßige Beiträge erstellen.</strong></p>
                                        <ul class="text-left pl-3">
                                            <li>Beiträge haben immer ein Fälligkeitsdatum und werden nur Mitgliedern zugewiesen, die zu dem Zeitpunkt auch im Team waren.</li>
                                            <li>Neue Mitglieder bekommen automatisch zukünftige Beiträge zugeordnet. Wird ein Mitglied gelöscht, werden auch zukünftige Beiträge gelöscht.</li>
                                            <li>Ein Beitrag wurde schon bezahlt, nachträglich wird aber entschieden, dass die Person davon befreit wird? Du kannst direkt angeben, ob der Betrag als Guthaben verbleibt oder ausbezahlt wird.</li>
                                            <li>Der Tag der Strafe ändert sich aber letzte Woche wurde der Betrag für die Strafe angepasst? Kein Problem, numo nimmt immer den zum Fälligkeitsdatum gültigen Betrag.
                                                <ul class="pl-4">
                                                    <li>Der neue Betrag ist höher? Wenn Guthaben vorhanden ist, wird es verwendet. Ansonsten wird die Strafe als nicht erfüllt markiert und der aktuelle Betrag als Guthaben notiert.</li>
                                                    <li>Der neue Betrag ist niedriger? Die Differenz wird direkt als Guthaben notiert.</li>
                                                </ul>
                                            </li>
                                            <li>Wann immer Guthaben bei einer Änderung entsteht, wird versucht andere offenen Strafen oder Beiträge davon zu begleichen.</li>
                                        </ul>
                                        
                                        {{--                                        TODO: Bild / Video von Balance Overview--}}
                                        {{--                                        <x-iphone-video-container src="{{Vite::video('features/statistik-anzeigen.webm')}}"/>--}}
                                    </div>
                                    <div class="tab-pane fade show" id="feature-teamkasse-strafenkatalog" role="tabpanel" aria-labelledby="feature-teamkasse-strafenkatalog-tab">
                                        <p>Hier kannst du Strafen erstellen, ändern und löschen<br>
                                        Egal was du änderst, numo kümmert sich um alles weitere und zeigt dir vorher immer genau was passiert.
                                        </p>
                                        <ul class="text-left pl-3">
                                            <li>Eine Strafe kann ein Betrag (z.B. 5€) oder ein Gegenstand (z.B. Kuchen oder Kasten Bier) sein.</li>
                                            <li>Strafen können jederzeit geändert werden. Einfach den Betrag oder Gegenstand anpassen und auswählen, ob die Strafe rückwirkend oder ab einem bestimmten Datum geändert wird.</li>
                                            <li>Alle bereits erfassten Strafen werden automatisch angepasst:
                                                <ul class="pl-4">
                                                    <li>Der neue Betrag ist höher? Wenn Guthaben vorhanden ist, wird es verwendet. Ansonsten werden die Strafe als nicht erfüllt markiert und der aktuelle Betrag als Guthaben notiert.</li>
                                                    <li>Der neue Betrag ist niedriger? Die Differenz wird direkt als Guthaben notiert.</li>
                                                </ul>
                                            </li>
                                            <li>Wann immer Guthaben bei einer Änderung entsteht, wird versucht andere offenen Strafen oder Beiträge davon zu begleichen.</li>
                                            <li>Änderungen können auch wieder gelöscht werden, dann wird die vorhergehende Version wieder gültig und bereits erfasste Strafen automatisch angepasst.</li>
                                            <li>Wird die ganze Strafe gelöscht, werden auch alle erfassten Strafen gelöscht und die Beträge als Guthaben für die Mitglieder notiert.<br>
                                                Aber keine Angst, vorher wird dir noch angezeigt, wie oft die Strafe bereits vergeben wurde. So siehst du immer vorab die Auswirkungen.</li>
                                        </ul>
                                        
                                        {{--                                        TODO: Bild / Video von Balance Overview--}}
                                        {{--                                        <x-iphone-video-container src="{{Vite::video('features/statistik-anzeigen.webm')}}"/>--}}
                                    </div>
                                    <div class="tab-pane fade show" id="feature-teamkasse-beitragskatalog" role="tabpanel" aria-labelledby="feature-teamkasse-beitragskatalog-tab">
                                        <p>Hier kannst du Beiträge erstellen, ändern und löschen<br>
                                        Egal was du änderst, numo kümmert sich um alles weitere und zeigt dir vorher immer genau was passiert.
                                        </p>
                                        <ul class="text-left pl-3">
                                            <li>Eine Beitrag kann ein Betrag (z.B. 5€) oder ein Gegenstand (z.B. Kuchen oder Kasten Bier) sein.</li>
                                            <li>Beiträge können einmalig oder regelmäßig sein. Egal ob jede Woche, jeden Monat, jeden zweiten Monat am 13. oder doch lieber alle 11 Tage, hier ist alles möglich.</li>
                                            <li>Beiträge haben immer ein Fälligkeitsdatum und werden nur Mitgliedern zugewiesen, die zu dem Zeitpunkt auch im Team waren.</li>
                                            <li>Beiträge können jederzeit geändert werden. Einfach den Betrag oder Gegenstand anpassen und bei regelmäßigen Beiträgen ggf. das Enddatum anpassen.</li>
                                            <li>Alle bereits zugewiesen Beiträge werden automatisch angepasst:
                                                <ul class="pl-4">
                                                    <li>Der neue Betrag ist höher? Wenn Guthaben vorhanden ist, wird es verwendet. Ansonsten werden die Beiträge als nicht erfüllt markiert und der aktuelle Betrag als Guthaben notiert.</li>
                                                    <li>Der neue Betrag ist niedriger? Die Differenz wird direkt als Guthaben notiert.</li>
                                                </ul>
                                            </li>
                                            <li>Wann immer Guthaben bei einer Änderung entsteht, wird versucht andere offenen Strafen oder Beiträge davon zu begleichen.</li>
                                            <li>Beiträge können zur Übersicht aus dem Katalog entfernt werden, dann wird das Enddatum auf heute gesetzt und zukünftige Beiträge entfernt.</li>
                                            <li>Wird der Beitrag vollständig gelöscht, werden auch alle zugewiesenen Beiträge gelöscht und die Beträge als Guthaben für die Mitglieder notiert.<br>
                                                Aber keine Angst, vorher wird dir noch angezeigt, wie oft der Beitrag bereits vergeben wurde. So siehst du immer vorab die Auswirkungen.</li>
                                        </ul>
                                        
                                        {{--                                        TODO: Bild / Video von Balance Overview--}}
                                        {{--                                        <x-iphone-video-container src="{{Vite::video('features/statistik-anzeigen.webm')}}"/>--}}
                                    </div>
                                    <div class="tab-pane fade show" id="feature-teamkasse-umsaetze" role="tabpanel" aria-labelledby="feature-teamkasse-umsaetze-tab">
                                        <p>Hier werden alle Transaktionen aufgeführt, nicht nur von den Mitgliedern,<br>
                                            auch alle anderen Ein- und Auszahlungen wie Spenden oder eure Ausgaben sind hier zu sehen.
                                        </p>
                                        <p>Hier kannst du auch neue Ein- Auszahlungen erstellen (für ein Mitglied oder mit Verwendungszwecke wenn ihr eine Spende bekommt oder den Deckel bezahlt).</p>
                                        <p>Transaktionen können NICHT bearbeitet werden! Hast du dich also vertippt, musst du eine weitere Buchung als Gegenbuchung eintragen.</p>
                                                                                
                                        {{--                                        TODO: Bild / Video von Balance Overview--}}
                                        {{--                                        <x-iphone-video-container src="{{Vite::video('features/statistik-anzeigen.webm')}}"/>--}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>

    <secction class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="section-content text-center">
                        <div class="card">
                            <div class="card-body section-blue justify-content-center text-center">
                                <h5 class="card-title">Starte noch heute mit deinem Team!</h5>
                                <span class="line"></span>
                                <p class="mb-3">Buche eine kostenlose 15-minütige Live Demo und erfahre mehr über unseren Vereinsrabatt!</p>
                                <div class="mb-3">
                                    <a href="https://meetings-eu1.hubspot.com/thomas-klaas" target="_blank" class="btn btn-light btn-lg">
                                        <i class="fa fa-video"></i> Kostenlose Live Demo
                                    </a>
                                </div>
                                <x-call-to-action-button/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </secction>

</x-main-layout-with-menu>

<script>
    $(document).ready(() => {
        $('a[data-toggle="tab"]').on("click", function() {
            let newUrl;
            const hash = $(this).attr("href");
            newUrl = url.hash = hash;
            history.replaceState(null, null, newUrl);
        });


        let url = new URL(location.href)
        if(url.hash) {
            const tabLink = $('.nav-tabs a[href="'+url.hash+'"]')
            if(tabLink.length > 0) {
                tabLink.tab("show");
                const scrollPosition = tabLink.closest('.card')
                if(scrollPosition.length  > 0) {
                    $('html, body').animate({
                        scrollTop: scrollPosition.offset().top-90
                    }, 900);
                }
            }
        }
    });
</script>
